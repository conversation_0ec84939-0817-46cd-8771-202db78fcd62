../../Scripts/datasets-cli.exe,sha256=J-CpNB2Q6GE7I29tcqq4tMt2vVkJRzheUjqmdLQcevE,108420
datasets-2.21.0.dist-info/AUTHORS,sha256=L0FBY23tCNHLmvsOKAbumHn8WZZIK98sH53JYxhAchU,327
datasets-2.21.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
datasets-2.21.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
datasets-2.21.0.dist-info/METADATA,sha256=rCYbMQZktkXsl1KZhjNUs2BXgti5oDcG1A2YjbTLjf8,21529
datasets-2.21.0.dist-info/RECORD,,
datasets-2.21.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets-2.21.0.dist-info/WHEEL,sha256=OqRkF0eY5GHssMorFjlbTIq072vpHpF60fIQA6lS9xA,92
datasets-2.21.0.dist-info/entry_points.txt,sha256=vhdg1JXUleCZtwvozP5q5iHqRpSETfyhSDJ39zW3KUA,70
datasets-2.21.0.dist-info/top_level.txt,sha256=9A857YvCQm_Dg3UjeKkWPz9sDBos0t3zN2pf5krTemQ,9
datasets/__init__.py,sha256=r4UhSnf5uVfYGXlLFFoSTv-e2NZRXr-zKn_Lq54NBPI,2965
datasets/__pycache__/__init__.cpython-311.pyc,,
datasets/__pycache__/arrow_dataset.cpython-311.pyc,,
datasets/__pycache__/arrow_reader.cpython-311.pyc,,
datasets/__pycache__/arrow_writer.cpython-311.pyc,,
datasets/__pycache__/builder.cpython-311.pyc,,
datasets/__pycache__/combine.cpython-311.pyc,,
datasets/__pycache__/config.cpython-311.pyc,,
datasets/__pycache__/data_files.cpython-311.pyc,,
datasets/__pycache__/dataset_dict.cpython-311.pyc,,
datasets/__pycache__/distributed.cpython-311.pyc,,
datasets/__pycache__/exceptions.cpython-311.pyc,,
datasets/__pycache__/fingerprint.cpython-311.pyc,,
datasets/__pycache__/hub.cpython-311.pyc,,
datasets/__pycache__/info.cpython-311.pyc,,
datasets/__pycache__/inspect.cpython-311.pyc,,
datasets/__pycache__/iterable_dataset.cpython-311.pyc,,
datasets/__pycache__/keyhash.cpython-311.pyc,,
datasets/__pycache__/load.cpython-311.pyc,,
datasets/__pycache__/metric.cpython-311.pyc,,
datasets/__pycache__/naming.cpython-311.pyc,,
datasets/__pycache__/search.cpython-311.pyc,,
datasets/__pycache__/splits.cpython-311.pyc,,
datasets/__pycache__/streaming.cpython-311.pyc,,
datasets/__pycache__/table.cpython-311.pyc,,
datasets/arrow_dataset.py,sha256=xkC1fDScBP4yr_SvIiLTkinZNOflsxlQZYtvnEV4v_o,301219
datasets/arrow_reader.py,sha256=xbU1VF-BBnX1qY8CaS9nlXILCExdPNtOuSI3TjoHM30,27236
datasets/arrow_writer.py,sha256=0rtyqfWyAHFijbqv17kdRqOkwE1yxP6-JD3vpYpolEU,33637
datasets/builder.py,sha256=ty0za1TEbknXxeFANeqG8POqPtY4lsF_C0a-dRcGMsA,111510
datasets/combine.py,sha256=OvMg-5A_cBraHyEXbNTTrWjd9sbUiyA7PG6aBJpbg5Q,10924
datasets/commands/__init__.py,sha256=rujbQtxJbwHhF9WQqp2DD9tfVTghDMJdl0v6H551Pcs,312
datasets/commands/__pycache__/__init__.cpython-311.pyc,,
datasets/commands/__pycache__/convert.cpython-311.pyc,,
datasets/commands/__pycache__/convert_to_parquet.cpython-311.pyc,,
datasets/commands/__pycache__/datasets_cli.cpython-311.pyc,,
datasets/commands/__pycache__/delete_from_hub.cpython-311.pyc,,
datasets/commands/__pycache__/dummy_data.cpython-311.pyc,,
datasets/commands/__pycache__/env.cpython-311.pyc,,
datasets/commands/__pycache__/run_beam.cpython-311.pyc,,
datasets/commands/__pycache__/test.cpython-311.pyc,,
datasets/commands/convert.py,sha256=-VOqHh0ySkIOfEYmR7HVs7PzouVrkVShqyUtNGcNCYU,7914
datasets/commands/convert_to_parquet.py,sha256=cCCug82MPSUiA_TUlJLFUhqGdaKNOL2NVpKQNtTvaCQ,1593
datasets/commands/datasets_cli.py,sha256=IYDSegUQgDa2ckXrwvLUXGm2517NKiUadLYkpBXWXI8,1648
datasets/commands/delete_from_hub.py,sha256=o0wdolb1r1Jnl6F0KdqKn3u0l8VR2od6KzbRoqrSNPM,1396
datasets/commands/dummy_data.py,sha256=rBVQAN1wd9fvldw79PVoL3vNZdqosjO_PPO_SFEYUqw,23106
datasets/commands/env.py,sha256=8qg-hpXSXXsHvtYFvJkn5rn9IncqPsjjx3nR8no4a2I,1239
datasets/commands/run_beam.py,sha256=Dg8migMADmQvUg0koc2MN-yOQts8olBw548gCejNuwM,7010
datasets/commands/test.py,sha256=VzxjshSCoLj9QjX5kSi5SUJDqLHClRJ5hMdMgOiEUmo,9127
datasets/config.py,sha256=gGmX6Lld74Y6pwDpBuxlyMnsJ8fXAAN_keMLy0lMwF4,10674
datasets/data_files.py,sha256=UcIKAbve3bht_J2bCJ_j6Wg4huDOi9K0mvlrmviC7MA,32464
datasets/dataset_dict.py,sha256=g9BJcdyztAK_ffd43JFUSRO7TL5SXOLKh4wnxDarsdc,105797
datasets/distributed.py,sha256=jZ31II0mmlPMhZbEtbAsX6jlK0U69qdpV3uS5U5JFYw,1560
datasets/download/__init__.py,sha256=lbFOtITDaR7PHrhzJ8VfRnpaOT6NYozSxUcLv_GVfTg,281
datasets/download/__pycache__/__init__.cpython-311.pyc,,
datasets/download/__pycache__/download_config.cpython-311.pyc,,
datasets/download/__pycache__/download_manager.cpython-311.pyc,,
datasets/download/__pycache__/mock_download_manager.cpython-311.pyc,,
datasets/download/__pycache__/streaming_download_manager.cpython-311.pyc,,
datasets/download/download_config.py,sha256=C5-DhnHAQeJfvvw1dl57Fk1o8cgrC7BySz4RqV6Lf9o,4957
datasets/download/download_manager.py,sha256=Fvz0FDBrmaH8gYfobcKeaT6p4gaCos9m0sjY3F8vKmg,17213
datasets/download/mock_download_manager.py,sha256=jpMYk8SFjqnoR9J-8qqldQyKCtzjCnUXKPkSp3og7DY,10351
datasets/download/streaming_download_manager.py,sha256=eqFKHDWSaP2bZpaDQIJbUIvZOSb6r6P1Kj4Ko7qGTVI,7339
datasets/exceptions.py,sha256=7aYLLIHMAEnjnQXCmE096f6Ap-6NdSDR13eJIr67ckY,6254
datasets/features/__init__.py,sha256=y1LKawSnzwOpfOegL_FvZLjP_Brgg3Tx52VXkHIT0Ks,459
datasets/features/__pycache__/__init__.cpython-311.pyc,,
datasets/features/__pycache__/audio.cpython-311.pyc,,
datasets/features/__pycache__/features.cpython-311.pyc,,
datasets/features/__pycache__/image.cpython-311.pyc,,
datasets/features/__pycache__/translation.cpython-311.pyc,,
datasets/features/audio.py,sha256=8_xpCxr5jyCM9zemFWTZK6mNfXv6VeF_3stNdQx0JFA,12225
datasets/features/features.py,sha256=zviQzLtIUemlU8Ts_r32VF15GXBSvdm_6ZBSLDE1hyk,92556
datasets/features/image.py,sha256=JoBseOcKuoa4d04xu-sQylvGWVURhZfJPml4pSTHDnQ,15526
datasets/features/translation.py,sha256=J6jxAcAPakmMwtaHhHAhDENi1AgIGmeNn4neuEeFWYg,4476
datasets/filesystems/__init__.py,sha256=bLz7EzLxZS9pqWp1Q8paS1g0waD05K44hBvKFwgIZ48,2268
datasets/filesystems/__pycache__/__init__.cpython-311.pyc,,
datasets/filesystems/__pycache__/compression.cpython-311.pyc,,
datasets/filesystems/__pycache__/s3filesystem.cpython-311.pyc,,
datasets/filesystems/compression.py,sha256=2NnuTGzqmH5wk_Vmp9nhuQCAAZ6bzBpCErvrHVOLR4c,4488
datasets/filesystems/s3filesystem.py,sha256=KowTCvTSsrdAU4syiaRffNw4g25-DTbjsoXBIMWz2tk,5725
datasets/fingerprint.py,sha256=pDq49L1aSrD9WXyfEVsR0rt28jDdW7rj7CiTykbIMRo,22040
datasets/formatting/__init__.py,sha256=K_egpF_SWGHL9H0hlob4lO6g214Sjh0fMG-7_V8w7eE,5391
datasets/formatting/__pycache__/__init__.cpython-311.pyc,,
datasets/formatting/__pycache__/formatting.cpython-311.pyc,,
datasets/formatting/__pycache__/jax_formatter.cpython-311.pyc,,
datasets/formatting/__pycache__/np_formatter.cpython-311.pyc,,
datasets/formatting/__pycache__/polars_formatter.cpython-311.pyc,,
datasets/formatting/__pycache__/tf_formatter.cpython-311.pyc,,
datasets/formatting/__pycache__/torch_formatter.cpython-311.pyc,,
datasets/formatting/formatting.py,sha256=jpUl_UlCT_AAFwhOnjoQYOr_3n4MpIRXL6C6CbMQCgY,26098
datasets/formatting/jax_formatter.py,sha256=KoTbq0XSUQ1Rp3G5IzN3cU192JZ9t5HAZtHiVpHPbB4,6839
datasets/formatting/np_formatter.py,sha256=DJBnt3oF0fHWJCqe4j6o9BOupZ0uGrw_xxFfsGBVoyk,4525
datasets/formatting/polars_formatter.py,sha256=PoOZM4RLFvAJdRZyNG5w3aOps3W3Saq1F8Mfyapgv8I,4700
datasets/formatting/tf_formatter.py,sha256=QRzeq8f1ALa6961PBNFRTH3RT4S-_8soqfUl9a7F89I,4657
datasets/formatting/torch_formatter.py,sha256=s6bP2ktOa8GXkjXq46odn1VpnzZhHN4Wrh7JUP9_3Y0,4728
datasets/hub.py,sha256=TFBvGkTXceEmaz0FDlP-mDmQLYLLCZ9T6GT0J8Nn3Gw,9380
datasets/info.py,sha256=R-o9Uv97SUoSI_SV4_HQQX7rJx6RHHWFfFmGIrnmeWg,26789
datasets/inspect.py,sha256=CFZ-Z1l9umEZRftcBNXecEq9I47gMAC-0JOCg3QCRqA,26400
datasets/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/io/__pycache__/__init__.cpython-311.pyc,,
datasets/io/__pycache__/abc.cpython-311.pyc,,
datasets/io/__pycache__/csv.cpython-311.pyc,,
datasets/io/__pycache__/generator.cpython-311.pyc,,
datasets/io/__pycache__/json.cpython-311.pyc,,
datasets/io/__pycache__/parquet.cpython-311.pyc,,
datasets/io/__pycache__/spark.cpython-311.pyc,,
datasets/io/__pycache__/sql.cpython-311.pyc,,
datasets/io/__pycache__/text.cpython-311.pyc,,
datasets/io/abc.py,sha256=LwDMXYs6YkhZuz1JiMK4PDIqgNjv7I8xH3UMUELW2ys,1672
datasets/io/csv.py,sha256=v4zaWehHb9U3njbdhy7wQnb8qO_c_58XOUC9JgBBVwI,5265
datasets/io/generator.py,sha256=sP_5GNozcxXIgDsPVMW_riqCZdInZ0_iFzcY_X1F-Mo,1909
datasets/io/json.py,sha256=_0PwV7ps8In3HQpNNJpYtdmafMEmQsF1lr74YuCRLlg,6459
datasets/io/parquet.py,sha256=qnPUUITsm-shWK2_6FcJE6rlRwivr97d7ghP0IT5QZA,5832
datasets/io/spark.py,sha256=VUIODLHgIbiK0CI0UvthQ_gUO0MQDtHUozvw7Dfs8FI,1797
datasets/io/sql.py,sha256=4Zjw7peVEhhzoDtz2VTCFPqt2Tpy4zMB7T7ajb2GVTY,4234
datasets/io/text.py,sha256=bebEzXBSGC40_Gy94j9ZTJ7Hg0IfrV_4pnIUEhQZVig,1975
datasets/iterable_dataset.py,sha256=ahg8OquFZBDAh-HavBe1NOvkGO8qfyVjxh5YjPeOkvA,140876
datasets/keyhash.py,sha256=gZLJ-0lIaj5mXP3fm0zFz8oY9L3Qu_OMkgil06oq0eg,3872
datasets/load.py,sha256=ucYyHwTpbFi4jVk9ZFong2lfFki4esc1oVJL0NiDG4c,127634
datasets/metric.py,sha256=BDyIxMAC7i9lGDrRcJjeMN1sxEcfXHGX29_SfTVfC3c,28065
datasets/naming.py,sha256=aqQqYG4QR8YoxJJMAUyVv_oQyudm4WAApsEHvcozpNg,3001
datasets/packaged_modules/__init__.py,sha256=Ot5gc8qiTJOBP4SSOLB4jRQboJuFdjonV7JiI0lxGz8,3748
datasets/packaged_modules/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/arrow/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/arrow/__pycache__/arrow.cpython-311.pyc,,
datasets/packaged_modules/arrow/arrow.py,sha256=GIEF10DPgfj2JE3UEi7REgU7Y6f7KiNgPWp4EnAUk5Q,3472
datasets/packaged_modules/audiofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/audiofolder/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/audiofolder/__pycache__/audiofolder.cpython-311.pyc,,
datasets/packaged_modules/audiofolder/audiofolder.py,sha256=t6dAb2CAXuGi_7KOf985XmypE12LkzHqeV19tAt39e8,1630
datasets/packaged_modules/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/cache/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/cache/__pycache__/cache.cpython-311.pyc,,
datasets/packaged_modules/cache/cache.py,sha256=XYXcLgZQRh8O85W-omwsnAJ9ZN3F1xz462PvU1n485o,8909
datasets/packaged_modules/csv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/csv/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/csv/__pycache__/csv.cpython-311.pyc,,
datasets/packaged_modules/csv/csv.py,sha256=82m39udsJK92n87jx1_vJYZ4HGKIm6JOKgQmowfkj1w,8580
datasets/packaged_modules/folder_based_builder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/folder_based_builder/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/folder_based_builder/__pycache__/folder_based_builder.cpython-311.pyc,,
datasets/packaged_modules/folder_based_builder/folder_based_builder.py,sha256=VH9AAlv81NjYS-6BII1VAx6AQ9Qa3xt4Z5Kz2vdRmU8,22571
datasets/packaged_modules/generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/generator/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/generator/__pycache__/generator.cpython-311.pyc,,
datasets/packaged_modules/generator/generator.py,sha256=atst9Zm2PJQV1ap8yabiGBijcc3q4ECZkS0I0cLskr8,1033
datasets/packaged_modules/imagefolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/imagefolder/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/imagefolder/__pycache__/imagefolder.cpython-311.pyc,,
datasets/packaged_modules/imagefolder/imagefolder.py,sha256=2giBUZHlLbJSv2Zx_YhmkQiFrtsSuMjKdM0GV8EtABs,2037
datasets/packaged_modules/json/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/json/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/json/__pycache__/json.cpython-311.pyc,,
datasets/packaged_modules/json/json.py,sha256=ipf8GieLlsGt5x1rJKr4ViJWg9oTHNp85OKYyPSW2R0,8698
datasets/packaged_modules/pandas/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/pandas/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/pandas/__pycache__/pandas.cpython-311.pyc,,
datasets/packaged_modules/pandas/pandas.py,sha256=eR0B5iGOHZ1owzezYmlvx5U_rWblmlpCt_PdC5Ax59E,2547
datasets/packaged_modules/parquet/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/parquet/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/parquet/__pycache__/parquet.cpython-311.pyc,,
datasets/packaged_modules/parquet/parquet.py,sha256=m-M-YrS6uzdOxvbBpT8vw8lVs65FJuNiXgb5bN-uy08,4541
datasets/packaged_modules/spark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/spark/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/spark/__pycache__/spark.cpython-311.pyc,,
datasets/packaged_modules/spark/spark.py,sha256=SVvpgK9eaaOFSSM6ajoXgHcmRVzV2iutdA1aEzvz9IM,14626
datasets/packaged_modules/sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/sql/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/sql/__pycache__/sql.cpython-311.pyc,,
datasets/packaged_modules/sql/sql.py,sha256=CihTzJh3Z95a0WbEoCT159aUkGh-KsNhv62v5LctLXk,4514
datasets/packaged_modules/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/text/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/text/__pycache__/text.cpython-311.pyc,,
datasets/packaged_modules/text/text.py,sha256=w_BcPHmyMVDmn2kRDsLc9ASGW51KRyeALYYabm8Z4kg,6027
datasets/packaged_modules/webdataset/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/webdataset/__pycache__/__init__.cpython-311.pyc,,
datasets/packaged_modules/webdataset/__pycache__/_tenbin.cpython-311.pyc,,
datasets/packaged_modules/webdataset/__pycache__/webdataset.cpython-311.pyc,,
datasets/packaged_modules/webdataset/_tenbin.py,sha256=oovYsgR2R3eXSn1xSCLG3oTly1szKDP4UOiRp4ORdIk,8533
datasets/packaged_modules/webdataset/webdataset.py,sha256=T4R5MvlWqRhfP2d9SDQtBaes5vwjzkiwrd6eUwX7mZQ,9530
datasets/parallel/__init__.py,sha256=wiRFK4x67ez2vvmjwM2Sb9R1yFdf38laSarU9y0Bido,76
datasets/parallel/__pycache__/__init__.cpython-311.pyc,,
datasets/parallel/__pycache__/parallel.cpython-311.pyc,,
datasets/parallel/parallel.py,sha256=E-oOQ6zwKrkLFPwZ-3EOcr_aANJDhE-d6QTq7Mp7WvA,4738
datasets/search.py,sha256=oUh55M77KOxHU-V4ZFVosHCj3IaAOWJJ8bmkGD1aXxw,35606
datasets/splits.py,sha256=AY9zyECJ_C9W8ctGpqicrrIT1ZzKuea4BBs6hS1Nk_w,23445
datasets/streaming.py,sha256=A04UAT5VeFrAn7t6gODgnxb7DgJvONLW-22uESg6-Fo,6540
datasets/table.py,sha256=KE7Q3lCrKibV_CL7tJcZc9S-3HogZ57GcxJor2mWDFk,95544
datasets/tasks/__init__.py,sha256=ExUcieN-G7_ybwfGqi3-Kt3akv3hrnlCq_RwEosFhiY,1614
datasets/tasks/__pycache__/__init__.cpython-311.pyc,,
datasets/tasks/__pycache__/audio_classification.cpython-311.pyc,,
datasets/tasks/__pycache__/automatic_speech_recognition.cpython-311.pyc,,
datasets/tasks/__pycache__/base.cpython-311.pyc,,
datasets/tasks/__pycache__/image_classification.cpython-311.pyc,,
datasets/tasks/__pycache__/language_modeling.cpython-311.pyc,,
datasets/tasks/__pycache__/question_answering.cpython-311.pyc,,
datasets/tasks/__pycache__/summarization.cpython-311.pyc,,
datasets/tasks/__pycache__/text_classification.cpython-311.pyc,,
datasets/tasks/audio_classification.py,sha256=fkR37qfJfJRPgCizf9iDV-dBnsGmLo2V0w8JpMwyX0M,1297
datasets/tasks/automatic_speech_recognition.py,sha256=zbTTsLX5N-_Da5oucuk6zBZhDdhD4N5_rzsni9lT_vo,1309
datasets/tasks/base.py,sha256=SlYEeDS87jruZNNkDRgz-U4q7EUijePL-RTN14ngwsk,1095
datasets/tasks/image_classification.py,sha256=llF5_koN5APq7cF_WlGy5c9hAVspRlYCprXgwAa7kCc,1297
datasets/tasks/language_modeling.py,sha256=Vdor-TdCGdiMpaIPZr0fRvgNrt5_D-1JElXKGbfQhvI,581
datasets/tasks/question_answering.py,sha256=z8a80QRTsouUuIYVKQRDMTxOGeSK1QMycyDHxUW42zg,1105
datasets/tasks/summarization.py,sha256=adrpmvgfAjXCyDRdZnZ52h0FKql5-EWU61Z2-v6rN-w,772
datasets/tasks/text_classification.py,sha256=KvlddXxnnzzjCjJmyY3Z-e1G4dpTN0UXqlmZ1L0LrjU,1403
datasets/utils/__init__.py,sha256=PuZtB9YTbRyvdwubnsx-JGdHuMA7p0I0Rmh0E_uxYF0,999
datasets/utils/__pycache__/__init__.cpython-311.pyc,,
datasets/utils/__pycache__/_dataset_viewer.cpython-311.pyc,,
datasets/utils/__pycache__/_dill.cpython-311.pyc,,
datasets/utils/__pycache__/_filelock.cpython-311.pyc,,
datasets/utils/__pycache__/beam_utils.cpython-311.pyc,,
datasets/utils/__pycache__/cache.cpython-311.pyc,,
datasets/utils/__pycache__/deprecation_utils.cpython-311.pyc,,
datasets/utils/__pycache__/doc_utils.cpython-311.pyc,,
datasets/utils/__pycache__/download_manager.cpython-311.pyc,,
datasets/utils/__pycache__/experimental.cpython-311.pyc,,
datasets/utils/__pycache__/extract.cpython-311.pyc,,
datasets/utils/__pycache__/file_utils.cpython-311.pyc,,
datasets/utils/__pycache__/filelock.cpython-311.pyc,,
datasets/utils/__pycache__/hub.cpython-311.pyc,,
datasets/utils/__pycache__/info_utils.cpython-311.pyc,,
datasets/utils/__pycache__/logging.cpython-311.pyc,,
datasets/utils/__pycache__/metadata.cpython-311.pyc,,
datasets/utils/__pycache__/patching.cpython-311.pyc,,
datasets/utils/__pycache__/py_utils.cpython-311.pyc,,
datasets/utils/__pycache__/readme.cpython-311.pyc,,
datasets/utils/__pycache__/sharding.cpython-311.pyc,,
datasets/utils/__pycache__/stratify.cpython-311.pyc,,
datasets/utils/__pycache__/tf_utils.cpython-311.pyc,,
datasets/utils/__pycache__/tqdm.cpython-311.pyc,,
datasets/utils/__pycache__/track.cpython-311.pyc,,
datasets/utils/__pycache__/typing.cpython-311.pyc,,
datasets/utils/__pycache__/version.cpython-311.pyc,,
datasets/utils/_dataset_viewer.py,sha256=L9gqrMGS6FgmEJps2uBK7HFFPFENrL881ZKIVAiaF-E,4438
datasets/utils/_dill.py,sha256=0QphnYT5cKHJEn17Cs_i1XFYazIfJZUr5mm8ehee_bw,17136
datasets/utils/_filelock.py,sha256=yl4ZQupEUyPu7f8D2ZCXitIMlajDu322QcO7Fio8eQI,2370
datasets/utils/beam_utils.py,sha256=DvA0ZVrx4-T9iHpB9VpduKn435p4rFaJw0Ua5cKmpeI,2029
datasets/utils/cache.py,sha256=MWZxUAtKFniRLZ94tm8bMji_VT0wvq1MCVnSEvALgCg,10548
datasets/utils/deprecation_utils.py,sha256=hTHwlzRs92NfNVudH71LMpW70sjbsP5amebrIgi3A-U,3452
datasets/utils/doc_utils.py,sha256=HoSm0TFaQaCYGfDgNhpBJ4Xc2WQZuOD6dTxLd9D87fs,407
datasets/utils/download_manager.py,sha256=AXDA-dUNUOmmy4Z7e8A34BJtQPcbJhWSQuO4p5wnDWY,60
datasets/utils/experimental.py,sha256=JgOjaEY3RWZ--3u0-ry82gLCDUpudfBfl-hWZ46SyS4,1097
datasets/utils/extract.py,sha256=Pw00NNW-vbmTfHduB-YCBEaw8qEmR4z_Ira7ZMiSlXs,14189
datasets/utils/file_utils.py,sha256=vZ021_59U6IXgFt52oOsofFOphCbt_GnutSd1eX4HH8,65819
datasets/utils/filelock.py,sha256=H6C5dQGFCzVKyeDRRY8fZ4YGTEvvNd-MTjpL_sWYb5k,352
datasets/utils/hub.py,sha256=V2JGolL5VjFT0YiEhI0sxJED_9tGdvma7lH22d64S9I,130
datasets/utils/info_utils.py,sha256=gAzubjnQbE0YTzB3hf3Cipmx5wCBtOje3fPwjYdzVBE,4330
datasets/utils/logging.py,sha256=a9kgqN1Xo6HvsIPbrHY08n7cUukxQqd3vpwTubisL3E,5404
datasets/utils/metadata.py,sha256=EXuwMc0s3jgksgglAFYERpKUd5deEsjQZq5wlIImjUM,12440
datasets/utils/patching.py,sha256=iTeb7XG4faLJKNylq55EcZyCndUXU_XBDvOOkuDz_sc,4955
datasets/utils/py_utils.py,sha256=YsBGk9CGEEH3LOsHeQ2_xNKCLABeMl1YgKTjCI24Awg,27624
datasets/utils/readme.py,sha256=JFlaLMCGrIz0nQCdnYKUZk5d9D9DErEYfjtRrX9VzIw,12627
datasets/utils/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/utils/resources/__pycache__/__init__.cpython-311.pyc,,
datasets/utils/resources/creators.json,sha256=XtIpMZefgBOdTevRrQTkFiufbgCbp_iyxseyphYQkn0,257
datasets/utils/resources/languages.json,sha256=Z0rQNPsfje8zMi8KdvvwxF4APwwqcskJFUvhNiLAgPM,199138
datasets/utils/resources/multilingualities.json,sha256=02Uc8RtRzfl13l98Y_alZm5HuMYwPzL78B0S5a1X-8c,205
datasets/utils/resources/readme_structure.yaml,sha256=hNf9msoBZw5jfakQrDb0Af8T325TXdcaHsAO2MUcZvY,3877
datasets/utils/resources/size_categories.json,sha256=_5nAP7z8R6t7_GfER81QudFO6Y1tqYu4AWrr4Aot8S8,171
datasets/utils/sharding.py,sha256=FDi895opKH7XkpfIu-ag9PqBQo2PGx0tSO3Dg-gDAAs,4288
datasets/utils/stratify.py,sha256=uMwuCDRbW342vy-lXDHs6IQusOr7c9nOG3PpnWyzJO4,4091
datasets/utils/tf_utils.py,sha256=3q8xCfAxTvg5oFlfSi78JkakhL3BaU1znPAQScpJv2s,24471
datasets/utils/tqdm.py,sha256=44F0g2fBpJwShh1l88PP7Z8kBihFWA_Yee4sjiQSxes,4303
datasets/utils/track.py,sha256=0OmbkJwVDlM0_ocs5h30vr5oOmoQ6FTKDuEuw6b8CvE,1856
datasets/utils/typing.py,sha256=LznosIqUzjXgwbRLAGCv4_7-yZo7muYY42Y3495oz5I,224
datasets/utils/version.py,sha256=Z82cHpjTbQVJyWgnwSU8DsW2G0y-sSbSoOVeQrAds9k,3281
